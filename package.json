{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev-https": "next dev --turbopack -p 9002 --experimental-https", "build": "next build", "build:vercel": "next build", "build:cloudflare": "node scripts/build-cloudflare.js", "start": "next start", "start:cloudflare": "wrangler pages dev .vercel/output/static --compatibility-date=2024-12-30", "deploy:cloudflare": "wrangler pages deploy .vercel/output/static", "lint": "next lint", "typecheck": "tsc --noEmit", "cf:login": "wrangler login", "cf:preview": "npm run build:cloudflare && wrangler pages dev .vercel/output/static", "deploy:dual": "node scripts/deploy-dual.js", "deploy:all": "npm run deploy:dual", "setup:cicd": "node scripts/setup-cicd.js"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/supabase-js": "^2.45.0", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.19.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "jsqr": "^1.4.0", "lucide-react": "^0.475.0", "next": "15.3.3", "next-pwa": "^5.6.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@tanstack/react-query-devtools": "^5.81.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "patch-package": "^8.0.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "wrangler": "^4.24.3"}}